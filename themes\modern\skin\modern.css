@charset "utf-8";

/* ==================== 现代主题 - 集成CSS样式 ==================== */

/* CSS重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

/* CSS变量定义 - 现代配色方案 */
:root {
    /* 主色调 */
    --primary-color: #667eea;
    --primary-light: #764ba2;
    --primary-dark: #4c63d2;
    --secondary-color: #f093fb;
    --accent-color: #4facfe;
    
    /* 中性色 */
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --text-light: #a0aec0;
    
    /* 背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f7fafc;
    --bg-tertiary: #edf2f7;
    --bg-card: #ffffff;
    --bg-overlay: rgba(0, 0, 0, 0.5);
    
    /* 边框和阴影 */
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
    
    /* 渐变 */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-warm: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --gradient-cool: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* 圆角 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 9999px;
    
    /* 过渡 */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
}

/* 基础样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 链接样式 */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
    text-decoration: none;
}

/* 工具类 */
.clearfix::after {
    content: "";
    display: table;
    clear: both;
}

.blank10 {
    height: 10px;
    width: 100%;
    display: block;
}

/* 容器样式 */
#wrapper {
    max-width: 1200px;
    margin: 0 auto;
    background: var(--bg-primary);
    box-shadow: var(--shadow-xl);
    border-radius: var(--radius-lg);
    overflow: hidden;
    position: relative;
}

/* 顶部栏样式 */
#topbar {
    background: var(--gradient-primary);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 12px;
}

#topbar-left {
    float: left;
}

#topbar-right {
    float: right;
}

#topbar-right img {
    vertical-align: middle;
    margin-left: var(--spacing-xs);
}

/* 头部样式 */
#header {
    background: var(--bg-primary);
    position: relative;
    z-index: 100;
}

#topbox {
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

/* Logo样式 */
.logo {
    width: 220px;
    height: 80px;
    background: url(../../../themes/default/skin/logo.png) center/contain no-repeat;
    display: block;
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
}

.logo:hover {
    transform: scale(1.05);
}

.logo::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: var(--transition-slow);
}

.logo:hover::before {
    left: 100%;
}

/* 搜索框样式 */
#sobox {
    flex: 1;
    max-width: 500px;
    margin: 0 var(--spacing-lg);
}

.sofrm {
    position: relative;
    display: flex;
    background: var(--bg-secondary);
    border-radius: var(--radius-full);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.sofrm:focus-within {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

#selopt {
    position: relative;
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--radius-full) 0 0 var(--radius-full);
    min-width: 120px;
}

#cursel {
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    font-weight: 500;
    text-align: center;
    transition: var(--transition-fast);
}

#cursel:hover {
    background: rgba(255, 255, 255, 0.1);
}

#options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    display: none;
    overflow: hidden;
    margin-top: var(--spacing-xs);
}

#options li {
    list-style: none;
}

#options li a {
    display: block;
    padding: var(--spacing-md);
    color: var(--text-primary);
    transition: var(--transition-fast);
}

#options li a:hover {
    background: var(--bg-secondary);
    color: var(--primary-color);
}

.sipt {
    flex: 1;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 14px;
    background: transparent;
    outline: none;
}

.sipt::placeholder {
    color: var(--text-muted);
}

.sbtn {
    background: var(--gradient-accent);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    border-radius: 0 var(--radius-full) var(--radius-full) 0;
}

.sbtn:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

/* 导航栏样式 */
#navbox {
    background: var(--gradient-primary);
    box-shadow: var(--shadow-md);
    position: sticky;
    top: 0;
    z-index: 99;
}

.navbar {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.navbar::-webkit-scrollbar {
    display: none;
}

.navbar li {
    flex-shrink: 0;
}

.navbar li a {
    display: block;
    padding: var(--spacing-md) var(--spacing-lg);
    color: white;
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.navbar li a::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: var(--secondary-color);
    transition: var(--transition-normal);
    transform: translateX(-50%);
}

.navbar li a:hover::before {
    width: 100%;
}

.navbar li a:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

/* 公告区域样式 */
#txtbox {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.count {
    font-size: 13px;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.count b {
    color: var(--primary-color);
    font-weight: 600;
    margin: 0 var(--spacing-xs);
}

.site-notice {
    margin: var(--spacing-sm) 0;
    overflow: hidden;
    height: 30px;
}

.site-notice ul {
    list-style: none;
    animation: scroll-up 10s infinite;
}

@keyframes scroll-up {
    0%, 30% { transform: translateY(0); }
    50%, 80% { transform: translateY(-30px); }
    100% { transform: translateY(0); }
}

.link {
    margin-top: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.quick-submit-link {
    background: var(--gradient-warm) !important;
    color: white !important;
    padding: var(--spacing-sm) var(--spacing-lg) !important;
    border-radius: var(--radius-full) !important;
    font-weight: 600 !important;
    text-decoration: none !important;
    box-shadow: var(--shadow-md) !important;
    transition: var(--transition-normal) !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: var(--spacing-xs) !important;
}

.quick-submit-link:hover {
    transform: translateY(-2px) scale(1.05) !important;
    box-shadow: var(--shadow-lg) !important;
}

/* 主体布局样式 */
#homebox {
    display: grid;
    grid-template-columns: 320px 1fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-xl);
    align-items: start;
}

#homebox-left {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

#homebox-right {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* VIP推荐区样式 */
#inbox1 {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

#inbox1 h3 {
    background: var(--gradient-primary);
    color: white;
    padding: var(--spacing-lg);
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    font-weight: 600;
}

.vip-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.donate-button {
    background: var(--gradient-warm);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.donate-button:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

.shine-effect {
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transform: skewX(-25deg);
    animation: shine 2s infinite;
}

@keyframes shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.inlist1 {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
}

.inlist1 li {
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    text-align: center;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.inlist1 li:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.inlist1 li::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
}

.inlist1 li img {
    width: 100%;
    height: 40px;
    object-fit: contain;
    margin-bottom: var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.inlist1 li a {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 13px;
}

.view-count-badge {
    position: absolute !important;
    top: var(--spacing-xs) !important;
    right: var(--spacing-xs) !important;
    background: var(--gradient-accent) !important;
    color: white !important;
    font-size: 10px !important;
    font-weight: 600 !important;
    padding: 2px 6px !important;
    border-radius: var(--radius-full) !important;
    box-shadow: var(--shadow-sm) !important;
    z-index: 10 !important;
}

/* 卡片样式组件 */
.modern-card {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition-normal);
}

.modern-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.modern-card-header {
    background: var(--gradient-primary);
    color: white;
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.modern-card-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.modern-card-body {
    padding: var(--spacing-lg);
}

/* 分类目录样式 */
#hcatebox {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

#hcatebox dt {
    background: var(--gradient-primary);
    color: white;
    padding: var(--spacing-lg);
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

#hcatebox dt a {
    color: white;
}

.hcatelist {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    list-style: none;
}

.hcatelist li a {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 13px;
    text-align: center;
    transition: var(--transition-fast);
    border: 2px solid transparent;
}

.hcatelist li a:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 最新收录样式 */
#newbox {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

#newbox h3 {
    background: var(--gradient-accent);
    color: white;
    padding: var(--spacing-lg);
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.newlist {
    padding: var(--spacing-lg);
    list-style: none;
}

.newlist li {
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: var(--transition-fast);
}

.newlist li:last-child {
    border-bottom: none;
}

.newlist li:hover {
    background: var(--bg-secondary);
    margin: 0 calc(-1 * var(--spacing-lg));
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
    border-radius: var(--radius-md);
}

.newlist li img {
    width: 18px;
    height: 18px;
    border-radius: var(--radius-sm);
    flex-shrink: 0;
}

.newlist li a {
    flex: 1;
    color: var(--text-primary);
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.newlist li span {
    color: var(--text-muted);
    font-size: 11px;
    flex-shrink: 0;
}

.new-icon {
    background: var(--gradient-secondary) !important;
    color: white !important;
    font-size: 8px !important;
    font-weight: 600 !important;
    padding: 2px 4px !important;
    border-radius: var(--radius-sm) !important;
    text-transform: uppercase !important;
    animation: pulse 2s infinite !important;
}

@keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.05); }
}

/* 热门标签样式 */
.hot-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
}

.hot-tag {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--radius-full);
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.hot-tag:nth-child(2n) { background: var(--gradient-accent); }
.hot-tag:nth-child(3n) { background: var(--gradient-secondary); }
.hot-tag:nth-child(4n) { background: var(--gradient-warm); }
.hot-tag:nth-child(5n) { background: var(--gradient-cool); }

.hot-tag:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-md);
    color: white;
}

.tag-count {
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-full);
    padding: 2px 6px;
    margin-left: var(--spacing-xs);
    font-size: 10px;
    font-weight: 600;
}

/* 右侧推荐区域样式 */
#bestbox {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.tab-header {
    background: var(--bg-secondary);
    display: flex;
    border-bottom: 1px solid var(--border-color);
}

.tab-item {
    flex: 1;
    padding: var(--spacing-md);
    text-align: center;
    cursor: pointer;
    transition: var(--transition-fast);
    border-bottom: 3px solid transparent;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
}

.tab-item.active {
    background: var(--bg-primary);
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-item:hover {
    background: var(--bg-primary);
    color: var(--primary-color);
}

.tab-content {
    position: relative;
}

.tab-pane {
    display: none;
    padding: var(--spacing-lg);
}

.tab-pane.active {
    display: block;
}

.bestlist-enhanced {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    list-style: none;
}

.recommend-item {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    transition: var(--transition-normal);
    border: 2px solid transparent;
}

.recommend-item:hover {
    background: var(--bg-primary);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.recommend-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-primary);
    text-decoration: none;
}

.recommend-icon {
    position: relative;
    flex-shrink: 0;
}

.recommend-icon img {
    width: 28px;
    height: 28px;
    border-radius: var(--radius-sm);
}

.recommend-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: var(--gradient-primary);
    color: white;
    font-size: 8px;
    font-weight: 600;
    padding: 2px 4px;
    border-radius: var(--radius-sm);
    text-transform: uppercase;
}

.pending-badge { background: var(--gradient-warm); }
.blacklist-badge { background: #e53e3e; }
.rejected-badge { background: #e53e3e; }

.recommend-name {
    flex: 1;
    font-size: 13px;
    font-weight: 500;
}

/* 底部区域样式 */
#inbox, #linkbox {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

#inbox h3, #linkbox h3 {
    color: var(--primary-color);
    font-size: 16px;
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.inlist, .linklist {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-sm);
    list-style: none;
}

.inlist li, .linklist li {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    overflow: hidden;
}

.inlist li:hover, .linklist li:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.inlist li a, .linklist li a {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    color: var(--text-primary);
    font-size: 13px;
    text-decoration: none;
}

.inlist li:hover a, .linklist li:hover a {
    color: white;
}

.inlist li img {
    width: 16px;
    height: 16px;
    border-radius: var(--radius-sm);
    flex-shrink: 0;
}

/* 页脚样式 */
#footer {
    background: var(--gradient-primary);
    color: white;
    padding: var(--spacing-xl);
    text-align: center;
    margin-top: var(--spacing-2xl);
}

#fmenu {
    margin-bottom: var(--spacing-lg);
}

#fmenu a {
    color: rgba(255, 255, 255, 0.8);
    margin: 0 var(--spacing-md);
    transition: var(--transition-fast);
}

#fmenu a:hover {
    color: white;
    text-decoration: underline;
}

#fcopy {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    line-height: 1.6;
}

/* 弹窗样式 */
.main-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-overlay);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.main-content {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    box-shadow: var(--shadow-xl);
    animation: popupSlideIn 0.3s ease-out;
}

@keyframes popupSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.main-close {
    position: absolute;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    background: var(--bg-secondary);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: var(--text-secondary);
    transition: var(--transition-fast);
}

.main-close:hover {
    background: var(--primary-color);
    color: white;
}

.main-title {
    color: var(--text-primary);
    font-size: 20px;
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.main-qr {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
}

.qr-item {
    text-align: center;
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
}

.qr-item h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: 16px;
    font-weight: 600;
}

.qr-image {
    width: 150px;
    height: 150px;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
}

/* 主题切换按钮样式 */
.theme-switcher {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    z-index: 1000;
    background: var(--gradient-primary);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: var(--radius-full);
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.theme-switcher:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--shadow-xl);
}

.theme-switcher::before {
    content: '🎨';
}

/* 响应式设计 */
@media (max-width: 1024px) {
    #wrapper {
        margin: 0;
        border-radius: 0;
    }

    #homebox {
        grid-template-columns: 1fr;
        padding: var(--spacing-lg);
    }

    #homebox-left {
        order: 2;
    }

    #homebox-right {
        order: 1;
    }
}

@media (max-width: 768px) {
    #topbox {
        flex-direction: column;
        text-align: center;
    }

    .logo {
        width: 180px;
        height: 60px;
    }

    #sobox {
        width: 100%;
        max-width: none;
        margin: var(--spacing-lg) 0 0 0;
    }

    .navbar {
        flex-wrap: wrap;
        justify-content: center;
    }

    .navbar li {
        flex: 1;
        min-width: 100px;
    }

    .inlist1 {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }

    .hcatelist {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }

    .bestlist-enhanced {
        grid-template-columns: 1fr;
    }

    .tab-header {
        flex-wrap: wrap;
    }

    .tab-item {
        flex: 1;
        min-width: 80px;
        font-size: 12px;
        padding: var(--spacing-sm);
    }

    .theme-switcher {
        right: 10px;
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    :root {
        --spacing-md: 0.75rem;
        --spacing-lg: 1rem;
        --spacing-xl: 1.25rem;
    }

    #homebox {
        padding: var(--spacing-md);
        gap: var(--spacing-lg);
    }

    .inlist1 {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: var(--spacing-sm);
    }

    .hcatelist {
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    }

    .main-content {
        padding: var(--spacing-lg);
        width: 95%;
    }

    .main-qr {
        grid-template-columns: 1fr;
    }
}
